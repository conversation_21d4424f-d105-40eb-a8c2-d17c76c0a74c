# Creative-Storyboard

A simple Next.js application with an Express backend demonstrating user registration and login with a MySQL database.

## Available Scripts

- `npm run dev` - start Next.js development server
- `npm run server` - start Express API server
- `npm test` - run tests (none provided)

The API server expects a MySQL database on `localhost` and uses the following table:

```sql
CREATE TABLE users (
  id INT AUTO_INCREMENT PRIMARY KEY,
  username VARCHAR(255) NOT NULL,
  email VARCHAR(255) NOT NULL UNIQUE,
  password VARCHAR(255) NOT NULL
);
```

Set environment variables in a `.env` file if needed:

```
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=yourpassword
DB_NAME=creative_db
API_PORT=3001
```
