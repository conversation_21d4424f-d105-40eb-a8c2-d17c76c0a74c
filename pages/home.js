import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import Navbar from '../components/Navbar';

export default function HomePage() {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [currentTime, setCurrentTime] = useState(new Date());
  const router = useRouter();

  useEffect(() => {
    const fetchUser = async () => {
      try {
        const res = await fetch('http://localhost:3001/api/home', {
          credentials: 'include'
        });
        if (res.ok) {
          const data = await res.json();
          setUser(data);
        } else {
          router.push('/login');
        }
      } catch (error) {
        console.error('Error fetching user:', error);
        router.push('/login');
      } finally {
        setLoading(false);
      }
    };

    fetchUser();

    // Update time every minute
    const timeInterval = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);

    return () => clearInterval(timeInterval);
  }, [router]);

  const getGreeting = () => {
    const hour = currentTime.getHours();
    if (hour < 12) return 'Good Morning';
    if (hour < 17) return 'Good Afternoon';
    return 'Good Evening';
  };

  const formatTime = () => {
    return currentTime.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  };

  const formatDate = () => {
    return currentTime.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <>
        <Navbar />
        <div className="container">
          <div className="spinner"></div>
        </div>
      </>
    );
  }

  return (
    <>
      <Navbar />
      <div className="container" style={{ paddingTop: '100px' }}>
        <div className="dashboard-container">
          {/* Welcome Header */}
          <div className="welcome-header">
            <div className="welcome-content">
              <h1>{getGreeting()}, {user?.username}! 🌟</h1>
              <p>Ready to create something amazing today?</p>
            </div>
          </div>

          {/* Quick Stats */}
          <div className="dashboard-grid">
            <div className="dashboard-card stats-card">
              <div className="card-icon">📁</div>
              <div className="card-content">
                <h3>Projects</h3>
                <div className="stat-number">12</div>
                <p className="stat-change">+2 this week</p>
              </div>
            </div>

            <div className="dashboard-card stats-card">
              <div className="card-icon">🎬</div>
              <div className="card-content">
                <h3>Storyboards</h3>
                <div className="stat-number">48</div>
                <p className="stat-change">+8 this week</p>
              </div>
            </div>

            <div className="dashboard-card stats-card">
              <div className="card-icon">🎭</div>
              <div className="card-content">
                <h3>Scenes</h3>
                <div className="stat-number">156</div>
                <p className="stat-change">+24 this week</p>
              </div>
            </div>

            <div className="dashboard-card stats-card">
              <div className="card-icon">⏱️</div>
              <div className="card-content">
                <h3>Hours Worked</h3>
                <div className="stat-number">32</div>
                <p className="stat-change">+6 this week</p>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="dashboard-grid">
            <div className="dashboard-card action-card">
              <div className="card-header">
                <h3>🚀 Quick Actions</h3>
              </div>
              <div className="action-buttons">
                <button className="action-btn">
                  <span className="action-icon">➕</span>
                  New Project
                </button>
                <button className="action-btn">
                  <span className="action-icon">🎨</span>
                  New Storyboard
                </button>
                <button className="action-btn">
                  <span className="action-icon">📋</span>
                  Templates
                </button>
                <button className="action-btn">
                  <span className="action-icon">📊</span>
                  Analytics
                </button>
              </div>
            </div>

            <div className="dashboard-card recent-card">
              <div className="card-header">
                <h3>📝 Recent Activity</h3>
              </div>
              <div className="activity-list">
                <div className="activity-item">
                  <div className="activity-icon">🎬</div>
                  <div className="activity-content">
                    <p><strong>Action Scene #3</strong> updated</p>
                    <span className="activity-time">2 hours ago</span>
                  </div>
                </div>
                <div className="activity-item">
                  <div className="activity-icon">📁</div>
                  <div className="activity-content">
                    <p><strong>Superhero Movie</strong> created</p>
                    <span className="activity-time">1 day ago</span>
                  </div>
                </div>
                <div className="activity-item">
                  <div className="activity-icon">🎭</div>
                  <div className="activity-content">
                    <p><strong>Character Design</strong> completed</p>
                    <span className="activity-time">2 days ago</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Progress Section */}
          <div className="dashboard-card progress-card">
            <div className="card-header">
              <h3>📈 This Week's Progress</h3>
            </div>
            <div className="progress-items">
              <div className="progress-item">
                <div className="progress-info">
                  <span>Storyboards Created</span>
                  <span>8/10</span>
                </div>
                <div className="progress-bar-container">
                  <div className="progress-bar-fill" style={{ width: '80%' }}></div>
                </div>
              </div>
              <div className="progress-item">
                <div className="progress-info">
                  <span>Scenes Completed</span>
                  <span>24/30</span>
                </div>
                <div className="progress-bar-container">
                  <div className="progress-bar-fill" style={{ width: '80%' }}></div>
                </div>
              </div>
              <div className="progress-item">
                <div className="progress-info">
                  <span>Projects Finished</span>
                  <span>2/3</span>
                </div>
                <div className="progress-bar-container">
                  <div className="progress-bar-fill" style={{ width: '67%' }}></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Floating Action Button */}
        <button className="fab" title="Create New">
          ➕
        </button>
      </div>
    </>
  );
}
